<!-- Edit Note Modal -->
<div class="modal fade" id="modal-edit-note" tabindex="-1" role="dialog" aria-labelledby="modal-edit-note-label" aria-hidden="true" wire:ignore.self data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-edit-note-label">Modifier la Note</h5>
                <button type="button" class="btn-close" onclick="closeEditNoteModal()" aria-label="Close"></button>
            </div>
            <div class="modal-body" wire:ignore>
                <?php if($showEditNoteModal && !empty($editNote)): ?>
                    <form id="editNoteForm" wire:submit.prevent="updateNote">
                        
                        <div class="mb-3">
                            <label class="form-label">Étudiant</label>
                            <input type="text" class="form-control" value="<?php echo e($editNote['student_name'] ?? 'N/A'); ?>" readonly disabled>
                        </div>

                        <div class="mb-3">
                            <label for="edit_matiere_id" class="form-label">Matière <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['editNote.matiere_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="edit_matiere_id" required>
                                <option value="">Sélectionner...</option>
                                <?php if(!empty($editNote['available_matieres'])): ?>
                                    <?php $__currentLoopData = $editNote['available_matieres']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $matiere): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($matiere['id']); ?>" <?php echo e(($editNote['matiere_id'] ?? '') == $matiere['id'] ? 'selected' : ''); ?>><?php echo e($matiere['nom']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                     
                                     <option value="" disabled>Chargement des matières...</option>
                                <?php endif; ?>
                            </select>
                            <?php $__errorArgs = ['editNote.matiere_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="edit_type_note_id" class="form-label">Type d'évaluation <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['editNote.type_note_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="edit_type_note_id" required>
                                <option value="">Sélectionner...</option>
                                <?php if(!empty($editNote['available_types'])): ?>
                                    <?php $__currentLoopData = $editNote['available_types']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($type['id']); ?>" <?php echo e(($editNote['type_note_id'] ?? '') == $type['id'] ? 'selected' : ''); ?>><?php echo e($type['nom']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                     <option value="" disabled>Chargement des types...</option>
                                <?php endif; ?>
                            </select>
                            <?php $__errorArgs = ['editNote.type_note_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="edit_valeur" class="form-label">Note (sur 20) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control <?php $__errorArgs = ['editNote.valeur'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="edit_valeur" value="<?php echo e($editNote['valeur'] ?? ''); ?>" min="0" max="20" step="0.01" required>
                            <?php $__errorArgs = ['editNote.valeur'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="edit_observation" class="form-label">Observation</label>
                            <textarea class="form-control" id="edit_observation" rows="3" placeholder="Observation optionnelle..."><?php echo e($editNote['observation'] ?? ''); ?></textarea>
                        </div>

                        <div class="modal-footer px-0 pb-0">
                            <button type="button" class="btn btn-alt-secondary" onclick="closeEditNoteModal()">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="submitEditNote()" id="saveEditNoteBtn">
                                <span class="spinner-border spinner-border-sm me-1 d-none" role="status" aria-hidden="true"></span>
                                Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                <?php else: ?>
                    
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Fonctions pour gérer le modal d'édition de note sans conflit Livewire
function closeEditNoteModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('modal-edit-note'));
    if (modal) {
        modal.hide();
    }
    // Notifier Livewire de la fermeture
    window.livewire.find('<?php echo e($_instance->id); ?>').call('closeEditNoteModal');
}

function submitEditNote() {
    const form = document.getElementById('editNoteForm');
    const saveBtn = document.getElementById('saveEditNoteBtn');
    const spinner = saveBtn.querySelector('.spinner-border');

    // Validation côté client
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Afficher le spinner
    spinner.classList.remove('d-none');
    saveBtn.disabled = true;

    // Récupérer les valeurs du formulaire
    const formData = {
        matiere_id: document.getElementById('edit_matiere_id').value,
        type_note_id: document.getElementById('edit_type_note_id').value,
        valeur: document.getElementById('edit_valeur').value,
        observation: document.getElementById('edit_observation').value
    };

    // Envoyer à Livewire
    window.livewire.find('<?php echo e($_instance->id); ?>').call('updateNoteFromModal', formData)
        .then(() => {
            // Succès - fermer le modal
            closeEditNoteModal();
        })
        .catch((error) => {
            console.error('Erreur lors de la mise à jour:', error);
            // Afficher un message d'erreur
            alert('Erreur lors de la mise à jour de la note. Veuillez réessayer.');
        })
        .finally(() => {
            // Masquer le spinner
            spinner.classList.add('d-none');
            saveBtn.disabled = false;
        });
}

// Empêcher la fermeture accidentelle du modal
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('modal-edit-note');
    if (modal) {
        modal.addEventListener('hide.bs.modal', function(event) {
            // Empêcher la fermeture si elle n'est pas intentionnelle
            if (!event.target.hasAttribute('data-allow-close')) {
                event.preventDefault();
                return false;
            }
        });
    }
});
</script>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/etudiant/modals/edit-note-modal.blade.php ENDPATH**/ ?>