<!-- Modal de saisie de note modernisé -->
<div class="modal fade" id="noteFormModal" tabindex="-1" aria-labelledby="noteFormModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="noteFormModalLabel">
                    <i class="fa fa-plus-circle me-2" id="modalIcon"></i>
                    <span id="modalTitle">Ajouter une note</span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Informations actuelles de la note (mode édition) -->
                <div id="currentNoteInfo" class="alert alert-info mb-4" style="display: none;">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="current-note-score" id="currentScore">
                                0.00
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading mb-1">
                                <i class="fa fa-info-circle me-1"></i>
                                Note actuelle
                            </h6>
                            <div class="row g-2 text-sm">
                                <div class="col-md-6">
                                    <strong>Matière:</strong> <span id="currentMatiere">-</span><br>
                                    <strong>Type:</strong> <span id="currentType">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>Date:</strong> <span id="currentDate">-</span><br>
                                    <strong>Dernière modif:</strong> <span id="currentUpdated">-</span>
                                </div>
                            </div>
                            <div class="mt-2" id="currentObservationContainer" style="display: none;">
                                <strong>Observation actuelle:</strong>
                                <div class="bg-light p-2 rounded mt-1">
                                    <em id="currentObservation">-</em>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fa fa-lightbulb me-1"></i>
                            Modifiez les champs ci-dessous pour mettre à jour cette note
                        </small>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="resetToCurrentValues()">
                            <i class="fa fa-undo me-1"></i>Restaurer
                        </button>
                    </div>
                </div>
                <form id="noteForm" wire:submit.prevent="saveNote">
                    <div class="row g-3">
                        <!-- Sélection du parcours -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-graduation-cap me-1 text-primary"></i>
                                Parcours <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" wire:model.defer="noteParcourId" onchange="handleParcourChange(this.value)">
                                <option value="">-- Sélectionner un parcours --</option>
                                <?php $__currentLoopData = $parcours ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($parc->id); ?>"><?php echo e($parc->sigle); ?> - <?php echo e($parc->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="invalid-feedback" id="parcours-error"></div>
                        </div>

                        <!-- Sélection de la matière -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-book me-1 text-primary"></i>
                                Matière <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" wire:model.defer="noteMatiereId" <?php if(!$noteParcourId): ?> disabled <?php endif; ?>>
                                <?php if(!$noteParcourId): ?>
                                    <option value="">-- Choisissez d'abord un parcours --</option>
                                <?php else: ?>
                                    <option value="">-- Sélectionner une matière --</option>
                                    <?php $__currentLoopData = $matieres ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($mat->id); ?>"><?php echo e($mat->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                            <div class="invalid-feedback" id="matiere-error"></div>
                        </div>

                        <!-- Type de note -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-tag me-1 text-primary"></i>
                                Type de note <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" wire:model.defer="noteTypeId">
                                <option value="">-- Sélectionner un type --</option>
                                <?php $__currentLoopData = $noteTypes ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($type->id); ?>"><?php echo e($type->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="invalid-feedback" id="type-error"></div>
                        </div>

                        <!-- Note sur 20 -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-star me-1 text-primary"></i>
                                Note sur 20 <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" wire:model.lazy="noteValeur" 
                                       min="0" max="20" step="0.01" placeholder="0.00">
                                <span class="input-group-text">/20</span>
                            </div>
                            <div class="invalid-feedback" id="valeur-error"></div>
                            
                            <!-- Indicateur visuel de la note -->
                            <div class="mt-2" id="noteIndicator" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="noteLabel"></small>
                                </div>
                            </div>
                        </div>

                        <!-- Observation -->
                        <div class="col-12">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-comment me-1 text-primary"></i>
                                Observation
                            </label>
                            <textarea class="form-control" wire:model.lazy="noteObservation" 
                                      rows="3" placeholder="Commentaire ou observation sur cette note..."></textarea>
                            <div class="form-text">
                                <i class="fa fa-info-circle me-1"></i>
                                Ajoutez des détails sur cette évaluation (optionnel)
                            </div>
                        </div>

                        <!-- Aperçu de la note -->
                        <div class="col-12" id="notePreview" style="display: none;">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fa fa-eye me-1"></i>Aperçu de la note
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Matière:</strong> <span id="previewMatiere">-</span><br>
                                        <strong>Type:</strong> <span id="previewType">-</span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Note:</strong> <span id="previewNote" class="fw-bold">-</span>/20<br>
                                        <strong>Appréciation:</strong> <span id="previewAppreciation">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <!-- Statut de sauvegarde -->
                        <span id="saveStatus" class="text-muted" style="display: none;">
                            <i class="fa fa-spinner fa-spin me-1"></i>Sauvegarde en cours...
                        </span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="fa fa-times me-1"></i>Annuler
                        </button>
                        <button type="submit" class="btn btn-primary" form="noteForm" id="saveBtn">
                            <i class="fa fa-save me-1"></i>
                            <span id="saveBtnText">Enregistrer</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.modal-header {
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 1rem 1rem;
    padding: 1.5rem;
}

.form-label {
    margin-bottom: 0.5rem;
    color: #495057;
}

.form-select, .form-control {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.input-group-text {
    border-radius: 0 0.5rem 0.5rem 0;
    border: 2px solid #e9ecef;
    border-left: none;
    background: #f8f9fa;
    font-weight: 600;
}

.progress {
    border-radius: 10px;
    background: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: all 0.3s ease;
}

.alert {
    border-radius: 0.75rem;
    border: none;
}

.btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Styles pour les informations actuelles de la note */
.current-note-score {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    color: white;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.current-note-score.excellent {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.current-note-score.good {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.current-note-score.average {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.current-note-score.poor {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
}

.current-note-score::after {
    content: '/20';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7rem;
    color: #6c757d;
    font-weight: 500;
}

#currentNoteInfo {
    border-left: 4px solid #0dcaf0;
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 202, 240, 0.05) 100%);
    border-radius: 0.75rem;
    position: relative;
    overflow: hidden;
}

#currentNoteInfo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #0dcaf0, #0d6efd);
}

.text-sm {
    font-size: 0.875rem;
}

#currentObservationContainer .bg-light {
    background: rgba(248, 249, 250, 0.8) !important;
    border: 1px solid #e9ecef;
}

/* Animation pour l'apparition des infos actuelles */
#currentNoteInfo {
    animation: slideInDown 0.4s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Indicateur de changement */
.field-changed {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

.field-changed + .form-text {
    color: #ffc107;
    font-weight: 600;
}

.field-changed + .form-text::before {
    content: '⚠️ ';
}

/* Bouton de restauration */
.btn-outline-primary:hover {
    background: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

/* Animations */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-body, .modal-footer {
        padding: 1rem;
    }
}
</style>

<script>
// Variables globales pour stocker les valeurs actuelles
let currentNoteData = null;
let isEditMode = false;

// Gestion de l'aperçu en temps réel et mode édition
document.addEventListener('DOMContentLoaded', function() {
    const noteInput = document.querySelector('input[wire\\:model\\.lazy="noteValeur"]');
    const matiereSelect = document.querySelector('select[wire\\:model="noteMatiereId"]');
    const typeSelect = document.querySelector('select[wire\\:model="noteTypeId"]');
    const observationTextarea = document.querySelector('textarea[wire\\:model\\.lazy="noteObservation"]');
    
    function updateNoteIndicator(value) {
        const indicator = document.getElementById('noteIndicator');
        const progressBar = indicator.querySelector('.progress-bar');
        const label = document.getElementById('noteLabel');
        
        if (value && value >= 0 && value <= 20) {
            indicator.style.display = 'block';
            const percentage = (value / 20) * 100;
            progressBar.style.width = percentage + '%';
            
            // Couleurs et labels selon la note
            if (value >= 16) {
                progressBar.className = 'progress-bar bg-success';
                label.textContent = 'Excellent';
            } else if (value >= 14) {
                progressBar.className = 'progress-bar bg-info';
                label.textContent = 'Bien';
            } else if (value >= 10) {
                progressBar.className = 'progress-bar bg-warning';
                label.textContent = 'Passable';
            } else {
                progressBar.className = 'progress-bar bg-danger';
                label.textContent = 'Insuffisant';
            }
        } else {
            indicator.style.display = 'none';
        }
    }
    
    function updatePreview() {
        const preview = document.getElementById('notePreview');
        const matiere = matiereSelect?.selectedOptions[0]?.text || '-';
        const type = typeSelect?.selectedOptions[0]?.text || '-';
        const note = noteInput?.value || '-';
        
        if (matiere !== '-' && type !== '-' && note !== '-') {
            document.getElementById('previewMatiere').textContent = matiere;
            document.getElementById('previewType').textContent = type;
            document.getElementById('previewNote').textContent = note;
            
            // Appréciation
            const appreciation = note >= 16 ? 'Excellent' : 
                               note >= 14 ? 'Bien' : 
                               note >= 10 ? 'Passable' : 'Insuffisant';
            document.getElementById('previewAppreciation').textContent = appreciation;
            
            preview.style.display = 'block';
        } else {
            preview.style.display = 'none';
        }
    }
    
    // Écouteurs d'événements
    if (noteInput) {
        noteInput.addEventListener('input', function() {
            updateNoteIndicator(this.value);
            updatePreview();
        });
    }
    
    if (matiereSelect) {
        matiereSelect.addEventListener('change', updatePreview);
    }
    
    if (typeSelect) {
        typeSelect.addEventListener('change', updatePreview);
    }

    if (observationTextarea) {
        observationTextarea.addEventListener('input', checkForChanges);
    }

    // Vérifier les changements sur tous les champs
    [noteInput, matiereSelect, typeSelect, observationTextarea].forEach(field => {
        if (field) {
            field.addEventListener('change', checkForChanges);
            field.addEventListener('input', checkForChanges);
        }
    });
});

// Fonction pour afficher les informations actuelles de la note
function showCurrentNoteInfo(noteData) {
    currentNoteData = noteData;
    isEditMode = true;

    // Mettre à jour le titre du modal
    document.getElementById('modalTitle').textContent = 'Modifier la note';
    document.getElementById('modalIcon').className = 'fa fa-edit me-2';
    document.querySelector('.modal-header').className = 'modal-header bg-warning text-dark';

    // Afficher les informations actuelles
    const currentInfo = document.getElementById('currentNoteInfo');
    currentInfo.style.display = 'block';

    // Mettre à jour le score avec la classe appropriée
    const scoreElement = document.getElementById('currentScore');
    scoreElement.textContent = parseFloat(noteData.valeur).toFixed(2);

    // Appliquer la classe de couleur selon la note
    scoreElement.className = 'current-note-score';
    if (noteData.valeur >= 16) {
        scoreElement.classList.add('excellent');
    } else if (noteData.valeur >= 14) {
        scoreElement.classList.add('good');
    } else if (noteData.valeur >= 10) {
        scoreElement.classList.add('average');
    } else {
        scoreElement.classList.add('poor');
    }

    // Mettre à jour les informations
    document.getElementById('currentMatiere').textContent = noteData.matiere?.nom || 'Non défini';
    document.getElementById('currentType').textContent = noteData.type_note?.nom || 'Non défini';
    document.getElementById('currentDate').textContent = new Date(noteData.created_at).toLocaleDateString('fr-FR');
    document.getElementById('currentUpdated').textContent = new Date(noteData.updated_at).toLocaleDateString('fr-FR');

    // Gérer l'observation
    const observationContainer = document.getElementById('currentObservationContainer');
    const observationElement = document.getElementById('currentObservation');

    if (noteData.observation && noteData.observation.trim() !== '') {
        observationElement.textContent = noteData.observation;
        observationContainer.style.display = 'block';
    } else {
        observationContainer.style.display = 'none';
    }
}

// Fonction pour masquer les informations actuelles (mode création)
function hideCurrentNoteInfo() {
    currentNoteData = null;
    isEditMode = false;

    // Restaurer le titre du modal
    document.getElementById('modalTitle').textContent = 'Ajouter une note';
    document.getElementById('modalIcon').className = 'fa fa-plus-circle me-2';
    document.querySelector('.modal-header').className = 'modal-header bg-primary text-white';

    // Masquer les informations actuelles
    document.getElementById('currentNoteInfo').style.display = 'none';

    // Réinitialiser les indicateurs de changement
    clearChangeIndicators();
}

// Fonction pour restaurer les valeurs actuelles
function resetToCurrentValues() {
    if (!currentNoteData) return;

    // Restaurer les valeurs via Livewire
    window.livewire.find('<?php echo e($_instance->id); ?>').set('noteValeur', currentNoteData.valeur);
    window.livewire.find('<?php echo e($_instance->id); ?>').set('noteMatiereId', currentNoteData.matiere_id);
    window.livewire.find('<?php echo e($_instance->id); ?>').set('noteTypeId', currentNoteData.type_note_id);
    window.livewire.find('<?php echo e($_instance->id); ?>').set('noteObservation', currentNoteData.observation || '');

    // Mettre à jour les champs du formulaire
    const noteInput = document.querySelector('input[wire\\:model\\.lazy="noteValeur"]');
    const matiereSelect = document.querySelector('select[wire\\:model="noteMatiereId"]');
    const typeSelect = document.querySelector('select[wire\\:model="noteTypeId"]');
    const observationTextarea = document.querySelector('textarea[wire\\:model\\.lazy="noteObservation"]');

    if (noteInput) noteInput.value = currentNoteData.valeur;
    if (matiereSelect) matiereSelect.value = currentNoteData.matiere_id;
    if (typeSelect) typeSelect.value = currentNoteData.type_note_id;
    if (observationTextarea) observationTextarea.value = currentNoteData.observation || '';

    // Supprimer les indicateurs de changement
    clearChangeIndicators();

    // Mettre à jour l'aperçu
    updateNoteIndicator(currentNoteData.valeur);
    updatePreview();

    showToast('info', 'Valeurs restaurées');
}

// Fonction pour vérifier les changements
function checkForChanges() {
    if (!isEditMode || !currentNoteData) return;

    const noteInput = document.querySelector('input[wire\\:model\\.lazy="noteValeur"]');
    const matiereSelect = document.querySelector('select[wire\\:model="noteMatiereId"]');
    const typeSelect = document.querySelector('select[wire\\:model="noteTypeId"]');
    const observationTextarea = document.querySelector('textarea[wire\\:model\\.lazy="noteObservation"]');

    // Vérifier chaque champ
    checkFieldChange(noteInput, currentNoteData.valeur);
    checkFieldChange(matiereSelect, currentNoteData.matiere_id);
    checkFieldChange(typeSelect, currentNoteData.type_note_id);
    checkFieldChange(observationTextarea, currentNoteData.observation || '');
}

// Fonction pour vérifier un champ spécifique
function checkFieldChange(field, originalValue) {
    if (!field) return;

    const currentValue = field.value;
    const hasChanged = currentValue != originalValue;

    if (hasChanged) {
        field.classList.add('field-changed');
    } else {
        field.classList.remove('field-changed');
    }
}

// Fonction pour supprimer tous les indicateurs de changement
function clearChangeIndicators() {
    document.querySelectorAll('.field-changed').forEach(field => {
        field.classList.remove('field-changed');
    });
}

// Fonction pour gérer le changement de parcours sans conflit Livewire
function handleParcourChange(value) {
    // Utiliser un délai pour éviter les conflits avec les événements Bootstrap
    setTimeout(() => {
        window.livewire.find('<?php echo e($_instance->id); ?>').call('updateParcour', value);
    }, 100);
}

// Événements Livewire pour gérer l'ouverture du modal
window.addEventListener('openNoteModal', event => {
    const modal = new bootstrap.Modal(document.getElementById('noteFormModal'));

    if (event.detail.mode === 'edit' && event.detail.noteData) {
        // Mode édition
        showCurrentNoteInfo(event.detail.noteData);
    } else {
        // Mode création
        hideCurrentNoteInfo();
    }

    modal.show();
});

window.addEventListener('closeNoteModal', event => {
    const modal = bootstrap.Modal.getInstance(document.getElementById('noteFormModal'));
    if (modal) {
        modal.hide();
    }
});

// Événement quand le modal se ferme
document.getElementById('noteFormModal').addEventListener('hidden.bs.modal', function() {
    hideCurrentNoteInfo();
    clearChangeIndicators();
});

// Empêcher la fermeture accidentelle du modal lors des interactions
document.getElementById('noteFormModal').addEventListener('hide.bs.modal', function(event) {
    // Vérifier si la fermeture est intentionnelle
    if (!event.target.hasAttribute('data-allow-close') && !event.relatedTarget) {
        // Si ce n'est pas une fermeture intentionnelle, l'empêcher
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'SELECT' || activeElement.tagName === 'INPUT')) {
            event.preventDefault();
            return false;
        }
    }
});

// Raccourcis clavier
document.addEventListener('keydown', function(e) {
    if (document.getElementById('noteFormModal').classList.contains('show')) {
        // Ctrl+S pour sauvegarder
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            document.getElementById('saveBtn').click();
        }

        // Ctrl+R pour restaurer (mode édition)
        if (e.ctrlKey && e.key === 'r' && isEditMode) {
            e.preventDefault();
            resetToCurrentValues();
        }

        // Escape pour fermer
        if (e.key === 'Escape') {
            const modal = bootstrap.Modal.getInstance(document.getElementById('noteFormModal'));
            modal.hide();
        }
    }
});
</script>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/components/note-form-modal.blade.php ENDPATH**/ ?>