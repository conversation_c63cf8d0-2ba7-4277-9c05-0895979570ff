<!-- Edit Note Modal -->
<div class="modal fade" id="modal-edit-note" tabindex="-1" role="dialog" aria-labelledby="modal-edit-note-label" aria-hidden="true" wire:ignore.self data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-edit-note-label">Modifier la Note</h5>
                <button type="button" class="btn-close" onclick="closeEditNoteModal()" aria-label="Close"></button>
            </div>
            <div class="modal-body" wire:ignore>
                @if($showEditNoteModal && !empty($editNote))
                    <form id="editNoteForm" wire:submit.prevent="updateNote">
                        {{-- Display student name for context --}}
                        <div class="mb-3">
                            <label class="form-label">Étudiant</label>
                            <input type="text" class="form-control" value="{{ $editNote['student_name'] ?? 'N/A' }}" readonly disabled>
                        </div>

                        <div class="mb-3">
                            <label for="edit_matiere_id" class="form-label">Matière <span class="text-danger">*</span></label>
                            <select class="form-select @error('editNote.matiere_id') is-invalid @enderror" id="edit_matiere_id" required>
                                <option value="">Sélectionner...</option>
                                @if(!empty($editNote['available_matieres']))
                                    @foreach($editNote['available_matieres'] as $matiere)
                                        <option value="{{ $matiere['id'] }}" {{ ($editNote['matiere_id'] ?? '') == $matiere['id'] ? 'selected' : '' }}>{{ $matiere['nom'] }}</option>
                                    @endforeach
                                @else
                                     {{-- Fallback or message if matieres aren't loaded --}}
                                     <option value="" disabled>Chargement des matières...</option>
                                @endif
                            </select>
                            @error('editNote.matiere_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label for="edit_type_note_id" class="form-label">Type d'évaluation <span class="text-danger">*</span></label>
                            <select class="form-select @error('editNote.type_note_id') is-invalid @enderror" id="edit_type_note_id" required>
                                <option value="">Sélectionner...</option>
                                @if(!empty($editNote['available_types']))
                                    @foreach($editNote['available_types'] as $type)
                                        <option value="{{ $type['id'] }}" {{ ($editNote['type_note_id'] ?? '') == $type['id'] ? 'selected' : '' }}>{{ $type['nom'] }}</option>
                                    @endforeach
                                @else
                                     <option value="" disabled>Chargement des types...</option>
                                @endif
                            </select>
                            @error('editNote.type_note_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label for="edit_valeur" class="form-label">Note (sur 20) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('editNote.valeur') is-invalid @enderror" id="edit_valeur" value="{{ $editNote['valeur'] ?? '' }}" min="0" max="20" step="0.01" required>
                            @error('editNote.valeur') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label for="edit_observation" class="form-label">Observation</label>
                            <textarea class="form-control" id="edit_observation" rows="3" placeholder="Observation optionnelle...">{{ $editNote['observation'] ?? '' }}</textarea>
                        </div>

                        <div class="modal-footer px-0 pb-0">
                            <button type="button" class="btn btn-alt-secondary" onclick="closeEditNoteModal()">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="submitEditNote()" id="saveEditNoteBtn">
                                <span class="spinner-border spinner-border-sm me-1 d-none" role="status" aria-hidden="true"></span>
                                Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                @else
                    {{-- Placeholder or loading state if modal is shown but data not ready --}}
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script>
// Fonctions pour gérer le modal d'édition de note sans conflit Livewire
function closeEditNoteModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('modal-edit-note'));
    if (modal) {
        modal.hide();
    }
    // Notifier Livewire de la fermeture
    @this.call('closeEditNoteModal');
}

function submitEditNote() {
    const form = document.getElementById('editNoteForm');
    const saveBtn = document.getElementById('saveEditNoteBtn');
    const spinner = saveBtn.querySelector('.spinner-border');

    // Validation côté client
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Afficher le spinner
    spinner.classList.remove('d-none');
    saveBtn.disabled = true;

    // Récupérer les valeurs du formulaire
    const formData = {
        matiere_id: document.getElementById('edit_matiere_id').value,
        type_note_id: document.getElementById('edit_type_note_id').value,
        valeur: document.getElementById('edit_valeur').value,
        observation: document.getElementById('edit_observation').value
    };

    // Envoyer à Livewire
    @this.call('updateNoteFromModal', formData)
        .then(() => {
            // Succès - fermer le modal
            closeEditNoteModal();
        })
        .catch((error) => {
            console.error('Erreur lors de la mise à jour:', error);
            // Afficher un message d'erreur
            alert('Erreur lors de la mise à jour de la note. Veuillez réessayer.');
        })
        .finally(() => {
            // Masquer le spinner
            spinner.classList.add('d-none');
            saveBtn.disabled = false;
        });
}

// Empêcher la fermeture accidentelle du modal
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('modal-edit-note');
    if (modal) {
        modal.addEventListener('hide.bs.modal', function(event) {
            // Empêcher la fermeture si elle n'est pas intentionnelle
            if (!event.target.hasAttribute('data-allow-close')) {
                event.preventDefault();
                return false;
            }
        });
    }
});
</script>
